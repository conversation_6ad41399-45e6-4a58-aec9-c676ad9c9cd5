"""
Deploy script for the Increment Smart Contract
This script deploys the increment contract to the BSV blockchain.
"""

import asyncio
import json
import os
from increment_contract import IncrementContract


# Configuration - Update these values before running
CONFIG = {
    # Replace with your private key in WIF format
    'private_key': 'L4oHTdDHHdWuGLY1ESxCV7eLbeHDv5oW3gzFSJCQrN6qKmtL9G2i',
    
    # UTXO to spend for deployment - get this from a wallet or block explorer
    'utxo_info': {
        'txid': '3d1636ea480c9f85371ff0b7021625e76b5f6e060acd8bfed35ba756a0e15819',  # Transaction ID containing the UTXO
        'hex': '010000000133775c5c98fca0842ee4ad0dd426622d4a6aeaa85b64d8495815032f106cd4c0000000006a47304402206bcd6462cab54fcbccefd9f05833f970b6183c901210a45b241f9a9696b0bbe902201098022c0223ca8df9861a8ed3fb5609723000724f96ef7509e2f877a129e59f4121021be86dfd195f31cd38e04e70d8c2f139047eca0ebc9b921da192f8b57290276cffffffff02a0860100000000001976a914a8281e9ffc461e1b54d89f0080f31b83cc33a17388ac68a10519000000001976a914f98374af8e3f73044a8a32513c677f4c2310ef4188ac00000000',   # Raw transaction hex
        'output_index': 0  # Output index to spend (usually 0 or 1)
    },
    
    # Initial contract parameters
    'initial_count': 0,
    'funding_satoshis': 2000,  # Amount to fund the contract with
    
    # Output file to save deployment info
    'deployment_file': 'contract_deployment.json'
}


def validate_config():
    """Validate the configuration before deployment."""
    errors = []
    
    if not CONFIG['private_key']:
        errors.append("Private key is required")
    
    if not CONFIG['utxo_info']['txid']:
        errors.append("UTXO txid is required")
        
    if not CONFIG['utxo_info']['hex']:
        errors.append("UTXO hex is required")
    
    if CONFIG['funding_satoshis'] < 1000:
        errors.append("Funding amount should be at least 1000 satoshis")
    
    if errors:
        print("❌ Configuration errors:")
        for error in errors:
            print(f"  - {error}")
        print("\nPlease update the CONFIG section in deploy.py")
        return False
    
    return True


def save_deployment_info(contract_txid: str, contract: IncrementContract):
    """Save deployment information to a JSON file."""
    deployment_info = {
        'contract_txid': contract_txid,
        'initial_count': contract.get_current_count(),
        'funding_satoshis': CONFIG['funding_satoshis'],
        'deployed_at': contract_txid,
        'private_key_address': contract.private_key.address(),
        'deployment_config': {
            'initial_count': CONFIG['initial_count'],
            'funding_satoshis': CONFIG['funding_satoshis']
        }
    }
    
    try:
        with open(CONFIG['deployment_file'], 'w') as f:
            json.dump(deployment_info, f, indent=2)
        print(f"📄 Deployment info saved to: {CONFIG['deployment_file']}")
    except Exception as e:
        print(f"⚠️  Warning: Could not save deployment info: {e}")


async def deploy_contract():
    """Deploy the increment contract."""
    print("🚀 Starting Increment Contract Deployment")
    print("=" * 50)
    
    # Validate configuration
    if not validate_config():
        return None
    
    try:
        # Create contract instance
        print(f"📝 Creating contract with initial count: {CONFIG['initial_count']}")
        contract = IncrementContract(
            private_key=CONFIG['private_key'],
            initial_count=CONFIG['initial_count']
        )
        
        print(f"💰 Contract will be funded with: {CONFIG['funding_satoshis']} satoshis")
        print(f"📍 Deploying from address: {contract.private_key.address()}")
        print()
        
        # Deploy the contract
        print("🔄 Broadcasting deployment transaction...")
        contract_txid = await contract.deploy(
            utxo_info=CONFIG['utxo_info'],
            funding_satoshis=CONFIG['funding_satoshis']
        )
        
        if contract_txid:
            print()
            print("🎉 Deployment Successful!")
            print("=" * 30)
            print(f"Contract TXID: {contract_txid}")
            print(f"Initial Count: {contract.get_current_count()}")
            print(f"Contract Address: {contract.private_key.address()}")
            print(f"Funding Amount: {CONFIG['funding_satoshis']} satoshis")
            print()
            
            # Save deployment information
            save_deployment_info(contract_txid, contract)
            
            print("📋 Next Steps:")
            print("1. Wait for the transaction to be confirmed on the blockchain")
            print("2. Use call.py to increment the contract counter")
            print(f"3. Check the deployment info in {CONFIG['deployment_file']}")
            print()
            print("🔗 View transaction on WhatsOnChain:")
            print(f"   https://whatsonchain.com/tx/{contract_txid}")
            
            return contract_txid
        else:
            print("❌ Deployment failed!")
            return None
            
    except Exception as e:
        print(f"💥 Deployment error: {e}")
        print("\nTroubleshooting tips:")
        print("1. Check that your private key is valid")
        print("2. Ensure the UTXO exists and is unspent")
        print("3. Verify the UTXO hex is correct")
        print("4. Make sure you have sufficient funds for fees")
        raise


def print_usage_instructions():
    """Print instructions for using the deploy script."""
    print("📖 Increment Contract Deployment Instructions")
    print("=" * 50)
    print()
    print("Before running this script, you need to:")
    print()
    print("1. 🔑 Set your private key:")
    print("   - Get a private key in WIF format")
    print("   - Update CONFIG['private_key'] in this file")
    print()
    print("2. 💰 Prepare a UTXO to spend:")
    print("   - Find an unspent output in your wallet")
    print("   - Get the transaction ID (txid)")
    print("   - Get the raw transaction hex")
    print("   - Note the output index (usually 0 or 1)")
    print("   - Update CONFIG['utxo_info'] in this file")
    print()
    print("3. ⚙️  Configure deployment parameters:")
    print("   - Set initial_count (default: 0)")
    print("   - Set funding_satoshis (minimum: 1000)")
    print()
    print("4. 🚀 Run the deployment:")
    print("   python deploy.py")
    print()
    print("Example UTXO info:")
    print("'utxo_info': {")
    print("    'txid': 'abc123...',")
    print("    'hex': '0100000001...',")
    print("    'output_index': 1")
    print("}")


if __name__ == "__main__":
    # Check if configuration is set up
    if not CONFIG['private_key'] or not CONFIG['utxo_info']['txid']:
        print_usage_instructions()
        print()
        print("⚠️  Please configure the script before running!")
    else:
        # Run the deployment
        try:
            result = asyncio.run(deploy_contract())
            if result:
                print(f"\n✅ Contract deployed successfully: {result}")
            else:
                print("\n❌ Deployment failed")
        except KeyboardInterrupt:
            print("\n🛑 Deployment cancelled by user")
        except Exception as e:
            print(f"\n💥 Unexpected error: {e}")

    # python3 source venv/bin/activate?
    # python3 deploy.py
