"""
Simple Increment Smart Contract for BSV
This contract maintains a counter that can be incremented on-chain.
"""

import asyncio
from typing import Optional
from bsv import (
    PrivateKey, P2PKH, Transaction, TransactionInput, TransactionOutput, Script
)
from bsv.script import OpReturn
from bsv.broadcasters.whatsonchain import WhatsOnChainBroadcaster
import json


class IncrementContract:
    """
    A simple smart contract that maintains an incrementable counter on the BSV blockchain.
    The contract state is stored in OP_RETURN outputs.
    """
    
    def __init__(self, private_key: str, initial_count: int = 0):
        """
        Initialize the increment contract.
        
        Args:
            private_key: Private key in WIF format
            initial_count: Initial value for the counter
        """
        self.private_key = PrivateKey(private_key)
        self.count = initial_count
        self.contract_txid: Optional[str] = None
        
    def create_state_script(self) -> Script:
        """
        Create an OP_RETURN script containing the contract state.
        
        Returns:
            Script containing the contract state
        """
        # Create a JSON representation of the contract state
        state_data = {
            "contract": "increment",
            "count": self.count,
            "version": 1
        }
        
        # Convert to bytes and create OP_RETURN script
        state_json = json.dumps(state_data, separators=(',', ':'))
        state_bytes = state_json.encode('utf-8')

        # OpReturn.lock() expects a list of data items
        return OpReturn().lock([state_bytes])
    
    def parse_state_from_script(self, script: Script) -> Optional[dict]:
        """
        Parse contract state from an OP_RETURN script.
        
        Args:
            script: The script to parse
            
        Returns:
            Dictionary containing the contract state, or None if invalid
        """
        try:
            # Extract data from OP_RETURN script
            script_hex = script.hex()
            
            # OP_RETURN scripts start with 6a (OP_RETURN opcode)
            if not script_hex.startswith('6a'):
                return None
                
            # Skip OP_RETURN opcode and length byte(s)
            # This is a simplified parser - in production you'd want more robust parsing
            data_start = 4  # Skip 6a and length byte
            data_hex = script_hex[data_start:]
            
            # Convert hex to bytes and then to string
            data_bytes = bytes.fromhex(data_hex)
            data_str = data_bytes.decode('utf-8')
            
            # Parse JSON
            state_data = json.loads(data_str)
            
            # Validate it's our contract
            if state_data.get('contract') == 'increment':
                return state_data
                
        except Exception:
            pass
            
        return None
    
    async def deploy(self, utxo_info: dict, funding_satoshis: int = 1000) -> str:
        """
        Deploy the contract to the blockchain.
        
        Args:
            utxo_info: Dictionary containing 'txid', 'hex', and 'output_index'
            funding_satoshis: Amount of satoshis to fund the contract with
            
        Returns:
            Transaction ID of the deployment transaction
        """
        try:
            # Create source transaction
            source_tx = Transaction.from_hex(utxo_info['hex'])
            
            # Create input from the UTXO
            tx_input = TransactionInput(
                source_transaction=source_tx,
                source_txid=utxo_info['txid'],
                source_output_index=utxo_info['output_index'],
                unlocking_script_template=P2PKH().unlock(self.private_key),
            )
            
            # Create contract state output (OP_RETURN)
            state_output = TransactionOutput(
                locking_script=self.create_state_script(),
                satoshis=0  # OP_RETURN outputs have 0 value
            )
            
            # Create funding output for the contract
            funding_output = TransactionOutput(
                locking_script=P2PKH().lock(self.private_key.address()),
                satoshis=funding_satoshis
            )

            # Create change output
            change_output = TransactionOutput(
                locking_script=P2PKH().lock(self.private_key.address()),
                change=True
            )
            
            # Create transaction
            tx = Transaction(
                [tx_input], 
                [state_output, funding_output, change_output], 
                version=1
            )
            
            # Set fee and sign
            tx.fee(100)  # 100 satoshis fee
            tx.sign()
            
            print(f"Deploying increment contract with initial count: {self.count}")
            print(f"Transaction fee: {tx.get_fee()} satoshis")
            print(f"Contract funding: {funding_satoshis} satoshis")
            
            # Broadcast transaction
            broadcaster = WhatsOnChainBroadcaster()
            response = await tx.broadcast(broadcaster=broadcaster)
            
            if response.status == "success":
                self.contract_txid = tx.txid()
                print("✅ Contract deployed successfully!")
                print(f"Contract TXID: {self.contract_txid}")
                print(f"Transaction hex: {tx.hex()}")
                return self.contract_txid
            else:
                print(f"❌ Deployment failed: {response}")
                if hasattr(response, 'description'):
                    print(f"Error: {response.description}")
                return None
                
        except Exception as e:
            print(f"Error deploying contract: {e}")
            raise
    
    async def increment(self, contract_utxo_info: dict) -> str:
        """
        Increment the counter in the contract.
        
        Args:
            contract_utxo_info: Dictionary containing contract UTXO info
            
        Returns:
            Transaction ID of the increment transaction
        """
        try:
            # Increment the counter
            self.count += 1
            
            # Create source transaction
            source_tx = Transaction.from_hex(contract_utxo_info['hex'])
            
            # Create input from the contract UTXO
            tx_input = TransactionInput(
                source_transaction=source_tx,
                source_txid=contract_utxo_info['txid'],
                source_output_index=contract_utxo_info['output_index'],
                unlocking_script_template=P2PKH().unlock(self.private_key),
            )
            
            # Create new state output with incremented count
            new_state_output = TransactionOutput(
                locking_script=self.create_state_script(),
                satoshis=0
            )
            
            # Maintain contract funding (reduced by fee)
            input_amount = source_tx.outputs[contract_utxo_info['output_index']].satoshis
            fee_amount = 100
            remaining_funding = input_amount - fee_amount

            funding_output = TransactionOutput(
                locking_script=P2PKH().lock(self.private_key.address()),
                satoshis=remaining_funding
            )

            # Create transaction
            tx = Transaction([tx_input], [new_state_output, funding_output], version=1)

            # Set fee and sign
            tx.fee(fee_amount)
            tx.sign()
            
            print(f"Incrementing contract counter to: {self.count}")
            print(f"Transaction fee: {tx.get_fee()} satoshis")
            
            # Broadcast transaction
            broadcaster = WhatsOnChainBroadcaster()
            response = await tx.broadcast(broadcaster=broadcaster)
            
            if response.status == "success":
                new_txid = tx.txid()
                print("✅ Contract incremented successfully!")
                print(f"New TXID: {new_txid}")
                print(f"Counter value: {self.count}")
                return new_txid
            else:
                print(f"❌ Increment failed: {response}")
                if hasattr(response, 'description'):
                    print(f"Error: {response.description}")
                # Revert the count increment on failure
                self.count -= 1
                return None
                
        except Exception as e:
            print(f"Error incrementing contract: {e}")
            # Revert the count increment on failure
            self.count -= 1
            raise
    
    def get_current_count(self) -> int:
        """Get the current counter value."""
        return self.count
    
    def get_contract_txid(self) -> Optional[str]:
        """Get the contract transaction ID."""
        return self.contract_txid


# Example usage
if __name__ == "__main__":
    # This is just for testing - actual usage should be in separate files
    print("Increment Contract module loaded successfully!")
    print("Use deploy.py to deploy the contract and call.py to increment it.")
