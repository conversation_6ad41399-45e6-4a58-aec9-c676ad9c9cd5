"""
Call script for the Increment Smart Contract
This script calls the increment function on the deployed contract.
"""

import asyncio
import json
import os
from p2pkh_increment_contract import IncrementContract
from bsv import Transaction


# Configuration - Update these values before running
CONFIG = {
    # Replace with your private key in WIF format (same as deployment)
    'private_key': 'L4oHTdDHHdWuGLY1ESxCV7eLbeHDv5oW3gzFSJCQrN6qKmtL9G2i',
    
    # Contract UTXO to spend - this will be updated from deployment info
    'contract_utxo_info': {
        'txid': '09424c4317cfd6e9a5e3efdb3d740adeda765154c9abe2d5921b9f6d284e8c44',  # Contract transaction ID
        'hex': '010000000185acaafeafa5e841a0da2d6621b9547af37c2e4656cd64e477fe53cb83e2a51a010000006b483045022100bb847d8a08d5aa68a6c3a7f53e66bbe21bfa522f74d32b89909f1b71644eec4a022013788c773581c014d40c6601c1fb1f205dc17b31e0be3e984c0f1da83ba55754412102ac90f611004b836381e24881ed6617f8184e15d6a4c9f60960be5fcc05b02facffffffff02000000000000000031006a2e7b22636f6e7472616374223a22696e6372656d656e74222c22636f756e74223a322c2276657273696f6e223a317d40060000000000001976a914a8281e9ffc461e1b54d89f0080f31b83cc33a17388ac00000000',   # Raw transaction hex
        'output_index': 1  # Output index of the funding output (usually 1)
    },
    # Files
    'deployment_file': 'contract_deployment.json',
    'call_history_file': 'contract_calls.json'
}


def parse_count_from_transaction(contract_utxo_info):
    """Parse the current count from the contract transaction OP_RETURN output."""
    try:
        # Get the transaction
        tx = Transaction.from_hex(contract_utxo_info['hex'])

        print(f"🔍 Debugging: Parsing transaction {contract_utxo_info['txid'][:16]}...")

        # Look for OP_RETURN output (usually output 0)
        for i, output in enumerate(tx.outputs):
            script_hex = output.locking_script.hex()
            print(f"   Output {i}: {script_hex[:50]}...")

            # Check if it's an OP_RETURN script
            if script_hex.startswith('006a'):  # OP_FALSE + OP_RETURN
                print(f"   Found OP_RETURN in output {i}")
                try:
                    # More robust parsing of OP_RETURN data
                    # Format: 00 6a [length] [data]
                    # Skip 00 (OP_FALSE) + 6a (OP_RETURN) + length byte(s)

                    # Skip OP_FALSE (00) and OP_RETURN (6a)
                    data_part = script_hex[4:]  # Skip first 4 chars (006a)

                    # Next byte(s) indicate length - for simplicity, assume single byte length
                    length_hex = data_part[:2]
                    length = int(length_hex, 16)
                    print(f"   Data length: {length} bytes")

                    # Extract the actual data
                    data_hex = data_part[2:2 + (length * 2)]
                    print(f"   Data hex: {data_hex}")

                    data_bytes = bytes.fromhex(data_hex)
                    data_str = data_bytes.decode('utf-8')
                    print(f"   Data string: {data_str}")

                    # Parse JSON
                    state_data = json.loads(data_str)
                    print(f"   Parsed JSON: {state_data}")

                    # Check if it's our contract and return count
                    if state_data.get('contract') == 'increment':
                        count = state_data.get('count', 0)
                        print(f"   ✅ Found count: {count}")
                        return count

                except Exception as e:
                    print(f"   ❌ Error parsing OP_RETURN data: {e}")
                    continue

        print("   ❌ No valid OP_RETURN found")
        return None

    except Exception as e:
        print(f"⚠️  Error parsing transaction: {e}")
        return None


def load_deployment_info():
    """Load deployment information from the JSON file."""
    try:
        if not os.path.exists(CONFIG['deployment_file']):
            print(f"❌ Deployment file not found: {CONFIG['deployment_file']}")
            print("Please run deploy.py first to deploy the contract.")
            return None
            
        with open(CONFIG['deployment_file'], 'r') as f:
            deployment_info = json.load(f)
            
        print(f"📄 Loaded deployment info from: {CONFIG['deployment_file']}")
        print(f"Contract TXID: {deployment_info['contract_txid']}")
        print(f"Initial Count: {deployment_info['initial_count']}")
        
        return deployment_info
        
    except Exception as e:
        print(f"❌ Error loading deployment info: {e}")
        return None


def save_call_history(call_info: dict):
    """Save call information to history file."""
    try:
        # Load existing history
        history = []
        if os.path.exists(CONFIG['call_history_file']):
            with open(CONFIG['call_history_file'], 'r') as f:
                history = json.load(f)
        
        # Add new call
        history.append(call_info)
        
        # Save updated history
        with open(CONFIG['call_history_file'], 'w') as f:
            json.dump(history, f, indent=2)
            
        print(f"📄 Call history updated: {CONFIG['call_history_file']}")
        
    except Exception as e:
        print(f"⚠️  Warning: Could not save call history: {e}")


def validate_config():
    """Validate the configuration before calling."""
    errors = []
    
    if not CONFIG['private_key']:
        errors.append("Private key is required")
    
    if not CONFIG['contract_utxo_info']['txid']:
        errors.append("Contract UTXO txid is required")
        
    if not CONFIG['contract_utxo_info']['hex']:
        errors.append("Contract UTXO hex is required")
    
    if errors:
        print("❌ Configuration errors:")
        for error in errors:
            print(f"  - {error}")
        print("\nPlease update the CONFIG section in call.py")
        return False
    
    return True


async def increment_contract():
    """Increment the contract counter."""
    print("🔄 Starting Contract Increment Call")
    print("=" * 40)
    
    # Load deployment information
    deployment_info = load_deployment_info()
    if not deployment_info:
        return None
    
    # Validate configuration
    if not validate_config():
        return None
    
    try:
        # Parse current count from the contract transaction
        current_count = parse_count_from_transaction(CONFIG['contract_utxo_info'])
        if current_count is None:
            print("❌ Could not parse current count from transaction")
            print("💡 Falling back to deployment initial count")
            current_count = deployment_info['initial_count']
        
        print(f"📝 Creating contract instance")
        contract = IncrementContract(
            private_key=CONFIG['private_key'],
            initial_count=current_count
        )
        
        print(f"📍 Contract address: {contract.private_key.address()}")
        print(f"🔢 Current count: {contract.get_current_count()}")
        print()
        
        # Call increment function
        print("🔄 Broadcasting increment transaction...")
        new_txid = await contract.increment(CONFIG['contract_utxo_info'])
        
        if new_txid:
            print()
            print("🎉 Increment Successful!")
            print("=" * 25)
            print(f"New TXID: {new_txid}")
            print(f"New Count: {contract.get_current_count()}")
            print(f"Previous Count: {current_count}")
            print()
            
            # Save call information
            call_info = {
                'txid': new_txid,
                'previous_count': current_count,
                'new_count': contract.get_current_count(),
                'previous_txid': CONFIG['contract_utxo_info']['txid'],
                'timestamp': new_txid  # Using txid as timestamp placeholder
            }
            save_call_history(call_info)
            
            print("📋 Next Steps:")
            print("1. Wait for the transaction to be confirmed")
            print("2. Update the contract UTXO info for the next call")
            print("3. Run this script again to increment further")
            print()
            print("🔗 View transaction on WhatsOnChain:")
            print(f"   https://whatsonchain.com/tx/{new_txid}")
            print()
            print("⚠️  Important: To call increment again, you need to:")
            print(f"   1. Update CONFIG['contract_utxo_info']['txid'] to: {new_txid}")
            print("   2. Get the new transaction hex and update 'hex'")
            print("   3. Verify the output_index (usually 1 for funding output)")
            print()
            print("💡 Quick update template:")
            print("'contract_utxo_info': {")
            print(f"    'txid': '{new_txid}',")
            print("    'hex': 'GET_FROM_WHATSONCHAIN',")
            print("    'output_index': 1")
            print("}")
            
            return new_txid
        else:
            print("❌ Increment call failed!")
            return None
            
    except Exception as e:
        print(f"💥 Increment error: {e}")
        print("\nTroubleshooting tips:")
        print("1. Check that the contract UTXO is unspent")
        print("2. Verify the transaction hex is correct")
        print("3. Ensure the output index points to the funding output")
        print("4. Make sure you have sufficient funds for fees")
        raise


def print_usage_instructions():
    """Print instructions for using the call script."""
    print("📖 Increment Contract Call Instructions")
    print("=" * 45)
    print()
    print("Before running this script, you need to:")
    print()
    print("1. 🚀 Deploy the contract first:")
    print("   - Run deploy.py to deploy the contract")
    print("   - This creates contract_deployment.json")
    print()
    print("2. 🔑 Set your private key:")
    print("   - Use the same private key as deployment")
    print("   - Update CONFIG['private_key'] in this file")
    print()
    print("3. 💰 Set the contract UTXO info:")
    print("   - Use the funding output from the deployment")
    print("   - Update CONFIG['contract_utxo_info'] in this file")
    print("   - txid: deployment transaction ID")
    print("   - hex: raw transaction hex of deployment")
    print("   - output_index: usually 1 (funding output)")
    print()
    print("4. 🔄 Run the increment call:")
    print("   python call.py")
    print()
    print("Example contract UTXO info:")
    print("'contract_utxo_info': {")
    print("    'txid': 'def456...',  # From deployment")
    print("    'hex': '0100000001...',  # Deployment tx hex")
    print("    'output_index': 1  # Funding output")
    print("}")


def show_call_history():
    """Show the history of contract calls."""
    try:
        if not os.path.exists(CONFIG['call_history_file']):
            print("📄 No call history found.")
            return
            
        with open(CONFIG['call_history_file'], 'r') as f:
            history = json.load(f)
            
        if not history:
            print("📄 Call history is empty.")
            return
            
        print("📋 Contract Call History:")
        print("=" * 30)
        for i, call in enumerate(history, 1):
            print(f"{i}. TXID: {call['txid']}")
            print(f"   Count: {call['previous_count']} → {call['new_count']}")
            print(f"   Previous TXID: {call['previous_txid']}")
            print()
            
    except Exception as e:
        print(f"❌ Error reading call history: {e}")


if __name__ == "__main__":
    import sys
    
    # Check for command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "history":
        show_call_history()
        sys.exit(0)
    
    # Check if configuration is set up
    if not CONFIG['private_key'] or not CONFIG['contract_utxo_info']['txid']:
        print_usage_instructions()
        print()
        print("⚠️  Please configure the script before running!")
        print("💡 Tip: Run 'python call.py history' to see call history")
    else:
        # Run the increment call
        try:
            result = asyncio.run(increment_contract())
            if result:
                print(f"\n✅ Contract incremented successfully: {result}")
            else:
                print("\n❌ Increment call failed")
        except KeyboardInterrupt:
            print("\n🛑 Call cancelled by user")
        except Exception as e:
            print(f"\n💥 Unexpected error: {e}")

     # source venv/bin/activate?
     # python3 call.py
     
