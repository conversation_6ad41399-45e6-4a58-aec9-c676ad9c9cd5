"""
Deploy script for the Increment Smart Contract
This script deploys the increment contract to the BSV blockchain.
"""

import asyncio
import json
import os
from p2pkh_increment_contract import IncrementContract


# Configuration - Update these values before running
CONFIG = {
    # Replace with your private key in WIF format
    'private_key': 'L5VQNb9ir1GUEcREVcmCgoF1AARHheQA9u5bwCpoMitJqgDJcg6j',
    
    # UTXO to spend for deployment - get this from a wallet or block explorer
    'utxo_info': {
        'txid': '61ad2f7f2adb4e9f57c99a0d64f7e5e9a8f5e822e928e9e4f9b812f26a583e46',  # Transaction ID containing the UTXO
        'hex': '010000000102e5cf67b0075ae66ca1278012e052b4a4c748c9e5533bcd5f5f08c75c0e5081010000006a4730440220777a1a1bcc69994acab6e5cbaeba0acb64b368afca90996f2dbf1e97dda8e335022018a570e042bbccdbb67303a1c8e0a2448cf22036cc9a61d95141a75a7fa600154121039c2fe6668beab98c6bbd67acefe5587b60cc16b9650682bb0e461e242dd5d91bffffffff02681a7215000000001976a9140456769dd7021e89b4265418087f319ef8cf4e3788aca0860100000000001976a9144e59ed2d12eba033b66c4b9c2f6f57963923988988ac00000000',   # Raw transaction hex
        'output_index': 1  # Output index to spend (usually 0 or 1)
    },
    
    # Initial contract parameters
    'initial_count': 0,
    'funding_satoshis': 3000,  # Amount to fund the contract with
    
    # Output file to save deployment info
    'deployment_file': 'contract_deployment.json'
}


def validate_config():
    """Validate the configuration before deployment."""
    errors = []
    
    if not CONFIG['private_key']:
        errors.append("Private key is required")
    
    if not CONFIG['utxo_info']['txid']:
        errors.append("UTXO txid is required")
        
    if not CONFIG['utxo_info']['hex']:
        errors.append("UTXO hex is required")
    
    if CONFIG['funding_satoshis'] < 1000:
        errors.append("Funding amount should be at least 1000 satoshis")
    
    if errors:
        print("❌ Configuration errors:")
        for error in errors:
            print(f"  - {error}")
        print("\nPlease update the CONFIG section in deploy.py")
        return False
    
    return True


def save_deployment_info(contract_txid: str, contract: IncrementContract):
    """Save deployment information to a JSON file."""
    deployment_info = {
        'contract_txid': contract_txid,
        'initial_count': contract.get_current_count(),
        'funding_satoshis': CONFIG['funding_satoshis'],
        'deployed_at': contract_txid,
        'private_key_address': contract.private_key.address(),
        'deployment_config': {
            'initial_count': CONFIG['initial_count'],
            'funding_satoshis': CONFIG['funding_satoshis']
        }
    }
    
    try:
        with open(CONFIG['deployment_file'], 'w') as f:
            json.dump(deployment_info, f, indent=2)
        print(f"📄 Deployment info saved to: {CONFIG['deployment_file']}")
    except Exception as e:
        print(f"⚠️  Warning: Could not save deployment info: {e}")


async def deploy_contract():
    """Deploy the increment contract."""
    print("🚀 Starting Increment Contract Deployment")
    print("=" * 50)
    
    # Validate configuration
    if not validate_config():
        return None
    
    try:
        # Create contract instance
        print(f"📝 Creating contract with initial count: {CONFIG['initial_count']}")
        contract = IncrementContract(
            private_key=CONFIG['private_key'],
            initial_count=CONFIG['initial_count']
        )
        
        print(f"💰 Contract will be funded with: {CONFIG['funding_satoshis']} satoshis")
        print(f"📍 Deploying from address: {contract.private_key.address()}")
        print()
        
        # Deploy the contract
        print("🔄 Broadcasting deployment transaction...")
        contract_txid = await contract.deploy(
            utxo_info=CONFIG['utxo_info'],
            funding_satoshis=CONFIG['funding_satoshis']
        )
        
        if contract_txid:
            print()
            print("🎉 Deployment Successful!")
            print("=" * 30)
            print(f"Contract TXID: {contract_txid}")
            print(f"Initial Count: {contract.get_current_count()}")
            print(f"Contract Address: {contract.private_key.address()}")
            print(f"Funding Amount: {CONFIG['funding_satoshis']} satoshis")
            print()
            
            # Save deployment information
            save_deployment_info(contract_txid, contract)
            
            print("📋 Next Steps:")
            print("1. Wait for the transaction to be confirmed on the blockchain")
            print("2. Use call.py to increment the contract counter")
            print(f"3. Check the deployment info in {CONFIG['deployment_file']}")
            print()
            print("🔗 View transaction on WhatsOnChain:")
            print(f"   https://whatsonchain.com/tx/{contract_txid}")
            
            return contract_txid
        else:
            print("❌ Deployment failed!")
            return None
            
    except Exception as e:
        print(f"💥 Deployment error: {e}")
        print("\nTroubleshooting tips:")
        print("1. Check that your private key is valid")
        print("2. Ensure the UTXO exists and is unspent")
        print("3. Verify the UTXO hex is correct")
        print("4. Make sure you have sufficient funds for fees")
        raise


def print_usage_instructions():
    """Print instructions for using the deploy script."""
    print("📖 Increment Contract Deployment Instructions")
    print("=" * 50)
    print()
    print("Before running this script, you need to:")
    print()
    print("1. 🔑 Set your private key:")
    print("   - Get a private key in WIF format")
    print("   - Update CONFIG['private_key'] in this file")
    print()
    print("2. 💰 Prepare a UTXO to spend:")
    print("   - Find an unspent output in your wallet")
    print("   - Get the transaction ID (txid)")
    print("   - Get the raw transaction hex")
    print("   - Note the output index (usually 0 or 1)")
    print("   - Update CONFIG['utxo_info'] in this file")
    print()
    print("3. ⚙️  Configure deployment parameters:")
    print("   - Set initial_count (default: 0)")
    print("   - Set funding_satoshis (minimum: 1000)")
    print()
    print("4. 🚀 Run the deployment:")
    print("   python deploy.py")
    print()
    print("Example UTXO info:")
    print("'utxo_info': {")
    print("    'txid': 'abc123...',")
    print("    'hex': '0100000001...',")
    print("    'output_index': 1")
    print("}")


if __name__ == "__main__":
    # Check if configuration is set up
    if not CONFIG['private_key'] or not CONFIG['utxo_info']['txid']:
        print_usage_instructions()
        print()
        print("⚠️  Please configure the script before running!")
    else:
        # Run the deployment
        try:
            result = asyncio.run(deploy_contract())
            if result:
                print(f"\n✅ Contract deployed successfully: {result}")
            else:
                print("\n❌ Deployment failed")
        except KeyboardInterrupt:
            print("\n🛑 Deployment cancelled by user")
        except Exception as e:
            print(f"\n💥 Unexpected error: {e}")

    # source venv/bin/activate? No. use source bsv_env/bin/activate?
    # python3 deploy.py
